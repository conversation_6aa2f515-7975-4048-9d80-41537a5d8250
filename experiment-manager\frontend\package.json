{"name": "experiment-manager-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"next": "14.0.0", "react": "^18", "react-dom": "^18", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "zustand": "^4.4.6", "react-hook-form": "^7.61.1", "@hookform/resolvers": "^5.2.1", "yup": "^1.6.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10", "postcss": "^8", "tailwindcss": "^3", "eslint": "^8", "eslint-config-next": "14.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@types/jest": "^29.5.8"}}