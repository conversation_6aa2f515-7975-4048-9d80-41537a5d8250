"""
测试前端自动启动功能
"""

import sys
import os
import time

# 添加父目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_frontend_start():
    """测试前端启动"""
    print("=" * 50)
    print("前端自动启动测试")
    print("=" * 50)
    
    try:
        from expmanager.server import ServerManager
        from expmanager.config import Config
        
        # 创建配置
        config = Config()
        config.verbose = True
        config.server_auto_start = True
        config.force_server_start = True
        
        # 创建服务器管理器
        manager = ServerManager(config)
        
        # 测试npm命令查找
        print("\n[1/4] 测试npm命令查找...")
        npm_cmd = manager._get_npm_command()
        if npm_cmd:
            print(f"[OK] 找到npm命令: {npm_cmd}")
        else:
            print("[ERROR] 未找到npm命令")
            return False
        
        # 查找服务器路径
        print("\n[2/4] 查找服务器路径...")
        server_path = manager._find_server_path()
        if server_path:
            print(f"[OK] 找到服务器路径: {server_path}")
        else:
            print("[ERROR] 未找到服务器路径")
            return False
        
        # 检查前端目录
        print("\n[3/4] 检查前端目录...")
        frontend_path = server_path / "frontend"
        if frontend_path.exists():
            print(f"[OK] 前端目录存在: {frontend_path}")
            
            # 检查package.json
            package_json = frontend_path / "package.json"
            if package_json.exists():
                print("[OK] package.json存在")
            else:
                print("[ERROR] package.json不存在")
                return False
                
            # 检查node_modules
            node_modules = frontend_path / "node_modules"
            if node_modules.exists():
                print("[OK] node_modules存在")
            else:
                print("[WARN] node_modules不存在，需要安装依赖")
        else:
            print("[ERROR] 前端目录不存在")
            return False
        
        # 测试前端启动
        print("\n[4/4] 测试前端启动...")
        print("正在启动前端服务...")
        
        # 调用前端启动方法
        manager._start_frontend(server_path)
        
        # 等待启动
        print("等待前端启动...")
        time.sleep(10)
        
        # 检查是否启动成功
        import requests
        try:
            response = requests.get("http://localhost:3000", timeout=5)
            if response.status_code == 200:
                print("[OK] 前端启动成功！")
                return True
            else:
                print(f"[WARN] 前端响应异常: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("[ERROR] 前端启动失败，无法连接")
            return False
        except Exception as e:
            print(f"[ERROR] 前端检查失败: {e}")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_frontend_start()
    
    print("\n" + "=" * 50)
    if success:
        print("[SUCCESS] 前端自动启动测试成功！")
    else:
        print("[FAILED] 前端自动启动测试失败")
    print("=" * 50)
