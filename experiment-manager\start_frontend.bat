@echo off
echo [INFO] 启动ExpManager前端服务...
cd /d "%~dp0frontend"

echo [INFO] 检查Node.js安装...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js未安装或不在PATH中
    echo 请安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo [INFO] 检查npm安装...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm未安装或不在PATH中
    pause
    exit /b 1
)

echo [INFO] 检查依赖是否已安装...
if not exist "node_modules" (
    echo [INFO] 安装依赖包...
    npm install
    if errorlevel 1 (
        echo [ERROR] 依赖安装失败
        pause
        exit /b 1
    )
)

echo [INFO] 设置环境变量...
set NEXT_PUBLIC_API_URL=http://localhost:8000

echo [INFO] 启动前端开发服务器...
echo 前端将在 http://localhost:3000 启动
echo 按 Ctrl+C 停止服务器

REM 强制使用3000端口
npm run dev -- --port 3000

pause
